{"name": "diy-planner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@vueuse/core": "^13.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.9.0", "lucide-vue-next": "^0.513.0", "pinia": "^3.0.3", "postcss": "^8.5.4", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.4", "vue": "^3.5.13", "vue-router": "^4.5.1", "zod": "^3.25.57"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "^5.0.0", "@storybook/addon-a11y": "^9.0.6", "@storybook/addon-docs": "^9.0.6", "@storybook/addon-onboarding": "^9.0.6", "@storybook/addon-vitest": "^9.0.6", "@storybook/vue3-vite": "^9.0.6", "@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.3", "@vitest/browser": "^3.2.2", "@vitest/coverage-v8": "^3.2.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "playwright": "^1.52.0", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.2.2", "vue-tsc": "^2.2.8"}}