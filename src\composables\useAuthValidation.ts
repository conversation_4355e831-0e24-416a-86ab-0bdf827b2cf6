import { z } from 'zod'
import { ref, computed } from 'vue'

// Validation schemas
export const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(1, 'Password is required')
})

export const signUpSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^a-zA-Z0-9]/, 'Password must contain at least one special character')
})

// Type definitions
export type SignInFormData = z.infer<typeof signInSchema>
export type SignUpFormData = z.infer<typeof signUpSchema>

// Validation errors type
export type ValidationErrors<T> = Partial<Record<keyof T, string>>

// Composable for form validation
export function useAuthValidation() {
  // Sign In validation
  const validateSignIn = (data: Partial<SignInFormData>) => {
    const result = signInSchema.safeParse(data)
    
    if (result.success) {
      return { isValid: true, errors: {}, data: result.data }
    }
    
    const errors: ValidationErrors<SignInFormData> = {}
    result.error.errors.forEach((error) => {
      const field = error.path[0] as keyof SignInFormData
      if (field && !errors[field]) {
        errors[field] = error.message
      }
    })
    
    return { isValid: false, errors, data: null }
  }

  // Sign Up validation
  const validateSignUp = (data: Partial<SignUpFormData>) => {
    const result = signUpSchema.safeParse(data)
    
    if (result.success) {
      return { isValid: true, errors: {}, data: result.data }
    }
    
    const errors: ValidationErrors<SignUpFormData> = {}
    result.error.errors.forEach((error) => {
      const field = error.path[0] as keyof SignUpFormData
      if (field && !errors[field]) {
        errors[field] = error.message
      }
    })
    
    return { isValid: false, errors, data: null }
  }

  // Field-level validation for real-time feedback
  const validateField = <T>(schema: z.ZodSchema<T>, fieldName: keyof T, value: any) => {
    try {
      const fieldSchema = schema.shape[fieldName as string]
      if (fieldSchema) {
        fieldSchema.parse(value)
        return null // No error
      }
      return null
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Invalid value'
      }
      return 'Invalid value'
    }
  }

  // Password strength indicator
  const getPasswordStrength = (password: string) => {
    if (!password) return { score: 0, label: '', color: '' }
    
    let score = 0
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[^a-zA-Z0-9]/.test(password)
    }
    
    score = Object.values(checks).filter(Boolean).length
    
    const strengthMap = {
      0: { label: 'Very Weak', color: 'text-red-500' },
      1: { label: 'Weak', color: 'text-red-400' },
      2: { label: 'Fair', color: 'text-yellow-500' },
      3: { label: 'Good', color: 'text-yellow-400' },
      4: { label: 'Strong', color: 'text-green-400' },
      5: { label: 'Very Strong', color: 'text-green-500' }
    }
    
    return {
      score,
      checks,
      ...strengthMap[score as keyof typeof strengthMap]
    }
  }

  // Reactive form state management
  const createFormState = <T extends Record<string, any>>(initialData: T) => {
    const formData = ref<T>({ ...initialData })
    const errors = ref<ValidationErrors<T>>({})
    const touched = ref<Partial<Record<keyof T, boolean>>>({})
    
    const setFieldValue = (field: keyof T, value: any) => {
      formData.value[field] = value
      touched.value[field] = true
    }
    
    const setFieldError = (field: keyof T, error: string | null) => {
      if (error) {
        errors.value[field] = error
      } else {
        delete errors.value[field]
      }
    }
    
    const clearErrors = () => {
      errors.value = {}
    }
    
    const resetForm = () => {
      formData.value = { ...initialData }
      errors.value = {}
      touched.value = {}
    }
    
    const hasErrors = computed(() => Object.keys(errors.value).length > 0)
    const isFieldTouched = (field: keyof T) => touched.value[field] || false
    
    return {
      formData,
      errors,
      touched,
      setFieldValue,
      setFieldError,
      clearErrors,
      resetForm,
      hasErrors,
      isFieldTouched
    }
  }

  return {
    // Schemas
    signInSchema,
    signUpSchema,
    
    // Validation functions
    validateSignIn,
    validateSignUp,
    validateField,
    
    // Utilities
    getPasswordStrength,
    createFormState
  }
}
