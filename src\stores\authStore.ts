import { defineStore } from "pinia";
import { ref } from "vue";
import {
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
    signInWithPopup,
    GoogleAuthProvider,
    signOut,
    onAuthStateChanged,
} from 'firebase/auth';

import type { User } from "@/types/user";

import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db, auth } from '@/firebase';

export const useAuthStore = defineStore('auth', () => {
    const user = ref<User | null>(null);
    const isLoading = ref<boolean>(false);
    const error = ref<string | null>(null);

    const googleProvider = new GoogleAuthProvider();

    onAuthStateChanged(auth, async (firebaseUser) => {
        isLoading.value = true;
        try {
            if (firebaseUser) {
                const userDocRef = doc(db, 'users', firebaseUser.uid);
                const userDoc = await getDoc(userDocRef);
                const role = userDoc.exists() ? (userDoc.data()?.role as 'user' | 'admin' | undefined) || 'user' : 'user';                
                user.value = {
                    uid: firebaseUser.uid,
                    email: firebaseUser.email,
                    displayName: firebaseUser.displayName || 'User',
                    role
                };
            } else {
                user.value = null;
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'An error occurred';
            console.error(error.value);
        }
        isLoading.value = false;
    });

    const signUp = async (email: string, password: string, firstName: string, lastName: string) => {
        isLoading.value = true;
        error.value = null;
        try {
            const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);
            const userDocRef  = doc(db, 'users', firebaseUser.uid);
            const displayName = `${firstName} ${lastName}`;
            await setDoc(userDocRef, {
                email,
                displayName,
                role: 'user',
            });
            user.value = {
                uid: firebaseUser.uid,
                email,
                displayName,
                role: 'user',
            };
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'An error occurred';
            throw err;
        }
        isLoading.value = false;
    };

    const signIn = async (email: string, password: string) => {
        isLoading.value = true;
        error.value = null;
        try {
            const { user: firebaseUser } = await signInWithEmailAndPassword(auth, email, password);
            const userDocRef = doc(db, 'users', firebaseUser.uid);
            const userDoc = await getDoc(userDocRef);
            const role = userDoc.exists() ? (userDoc.data()?.role as 'user' | 'admin' |  'creator' | undefined) || 'user' : 'user';
            user.value = {
                uid: firebaseUser.uid,
                email: firebaseUser.email,
                displayName: firebaseUser.displayName || 'User',
                role,
            };
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'An error occurred';
            throw err;
        }
        isLoading.value = false;
    };

    const signInWithGoogle = async () => {
        isLoading.value = true;
        error.value = null;
        try {
            const { user: firebaseUser } = await signInWithPopup(auth, googleProvider);
            const userDocRef = doc(db, 'users', firebaseUser.uid);
            const userDoc = await getDoc(userDocRef);
            if (!userDoc.exists()) {
                await setDoc(userDocRef, {
                    email: firebaseUser.email,
                    displayName: firebaseUser.displayName || 'User',
                    role: 'user',
                });
            }
            const role = userDoc.exists() ? (userDoc.data()?.role as 'user' | 'admin' |  'creator' | undefined) || 'user' : 'user';
            user.value = {
                uid: firebaseUser.uid,
                email: firebaseUser.email,
                displayName: firebaseUser.displayName || 'User',
                role,
            };
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Google sign-in failed';
            throw err;
        }
        isLoading.value = false;
    }

    const signOutUser = async () => {
        isLoading.value = true;
        error.value = null;
        try {
            await signOut(auth);
            user.value = null;
        } catch (err) {
            error.value = err instanceof Error ? err.message : 'Sign-out failed';
            throw err;
        }
        isLoading.value = false;
    }

    return {
        user,
        isLoading,
        error,
        signUp,
        signIn,
        signInWithGoogle,
        signOutUser,
    };

})