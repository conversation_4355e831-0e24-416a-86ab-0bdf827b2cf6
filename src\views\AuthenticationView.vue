<script lang="ts">
export const description = 'A login page with a muted background color.'
</script>

<script setup lang="ts">
import { ref } from 'vue'
import { Drill } from 'lucide-vue-next'
import LoginForm from '@/components/LoginForm.vue'
import SignUpForm from '@/components/SignUpForm.vue'

const isSignUp = ref(false)

const toggleForm = () => {
  isSignUp.value = !isSignUp.value
}
</script>

<template>
  <div class="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
    <div class="flex w-full max-w-sm flex-col gap-6">
      <a href="#" class="flex items-center gap-2 self-center font-medium">
        <div class="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
          <Drill class="size-4" />
        </div>
        DIY Planner
      </a>
      <LoginForm v-if="!isSignUp" @toggle-form="toggleForm" />
      <SignUpForm v-else @toggle-form="toggleForm" />
    </div>
  </div>
</template>
