<script setup lang="ts">
import { ref, watch } from 'vue'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'vue-router'
import { z } from 'zod'
import { Drill } from 'lucide-vue-next'
import LoginForm from '@/components/LoginForm.vue'
import SignUpForm from '@/components/SignUpForm.vue'

const router = useRouter()
const authStore = useAuthStore()
const isSignUp = ref(false)

const toggleForm = () => {
  isSignUp.value = !isSignUp.value
}

watch(
  () => authStore.user,
  (user) => {
    if (user) {
      router.push('/') // TODO: navigate to app page
    }
  }
)
</script>

<template>
  <div class="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
    <div class="flex w-full max-w-sm flex-col gap-6">
      <a href="#" class="flex items-center gap-2 self-center font-medium">
        <div class="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
          <Drill class="size-4" />
        </div>
        DIY Planner
      </a>
      <LoginForm v-if="!isSignUp" @toggle-form="toggleForm" @google-login="authStore.signInWithGoogle()" />
      <SignUpForm v-else @toggle-form="toggleForm" @google-login="authStore.signInWithGoogle()" />
    </div>
  </div>
</template>
