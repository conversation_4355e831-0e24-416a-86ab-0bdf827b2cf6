<script setup lang="ts">
import { ref } from 'vue';
import {Menu, X} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import DarkModeToggle from '../ui/darkmode/DarkModeToggle.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const isAuthenticated = ref(false);
const isOpen = ref(false);

const navigateToSignIn = () => {
    router.push('/login');
}

const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Features', href: '/features' },
    { name: 'Pricing', href: '/pricing' },
    { name: 'About', href: '/about' },
]
</script>

<template>
  <section class="relative w-full flex items-center py-4 border-b border-zinc-200 dark:border-zinc-800">
    <div class="container mx-auto px-4 md:px-6 lg:px-8">
      <nav class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-2xl font-bold">DIY Planner</span>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <RouterLink
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            class="text-zinc-600 dark:text-zinc-200 hover:bg-zinc-100 dark:hover:bg-zinc-800 p-2 rounded-md"
          >
            {{ item.name }}
          </RouterLink>
          <Button @click="navigateToSignIn">Sign In</Button>
          <DarkModeToggle />
        </div>


        <!-- Mobile Navigation Button -->
        <div class="md:hidden flex space-x-2">
          <DarkModeToggle />
          <Button variant="ghost" size="icon" @click="isOpen = !isOpen">
            <Menu v-if="!isOpen" class="!h-5 !w-5" />
            <X v-else class="!h-5 !w-5" />
          </Button>
        </div>
      </nav>

      <!-- Mobile Navigation Menu -->
      <div
        v-if="isOpen"
        class="md:hidden absolute top-full left-0 w-full border-b border-zinc-200 dark:border-zinc-800 py-4">
        <div class="container mx-auto px-4">
          <div class="flex flex-col space-y-2">
            <RouterLink
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              class="text-zinc-600 dark:text-zinc-200 hover:bg-zinc-100 dark:hover:bg-zinc-800 p-2 rounded-md"
            >
              {{ item.name }}
            </RouterLink>
            <Button class="w-full" @click="navigateToSignIn">Sign In</Button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.router-link-exact-active {
  background: var(--color-zinc-200);
}

.dark .router-link-exact-active {
  background: var(--color-zinc-700);
}
</style>