<script setup lang="ts">
import DefaultLayout from '@/layouts/DefaultLayout.vue';
</script>

<template>
  <DefaultLayout>
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-center mb-8 text-zinc-900 dark:text-zinc-100">
          Features
        </h1>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Feature cards will be added here -->
          <div class="p-6 border border-zinc-200 dark:border-zinc-800 rounded-lg">
            <h3 class="text-xl font-semibold mb-3 text-zinc-900 dark:text-zinc-100">
              Custom Planning
            </h3>
            <p class="text-zinc-600 dark:text-zinc-400">
              Create personalized planning templates that fit your unique workflow and goals.
            </p>
          </div>
          
          <div class="p-6 border border-zinc-200 dark:border-zinc-800 rounded-lg">
            <h3 class="text-xl font-semibold mb-3 text-zinc-900 dark:text-zinc-100">
              Goal Tracking
            </h3>
            <p class="text-zinc-600 dark:text-zinc-400">
              Monitor your progress with visual indicators and milestone tracking.
            </p>
          </div>
          
          <div class="p-6 border border-zinc-200 dark:border-zinc-800 rounded-lg">
            <h3 class="text-xl font-semibold mb-3 text-zinc-900 dark:text-zinc-100">
              Flexible Layouts
            </h3>
            <p class="text-zinc-600 dark:text-zinc-400">
              Choose from various layout options to organize your content the way you want.
            </p>
          </div>
        </div>
      </div>
    </div>
  </DefaultLayout>
</template>
