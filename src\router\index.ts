import { createWebHistory, createRouter } from "vue-router";

import HomeView from "@/views/HomeView.vue";
import FeaturesView from "@/views/FeaturesView.vue";
import PricingView from "@/views/PricingView.vue";
import AboutView from "@/views/AboutView.vue";
import AuthenticationView from "@/views/AuthenticationView.vue";

const routes = [
    {
        path: "/",
        name: "home",
        component: HomeView,
    },
    {
        path: "/features",
        name: "features",
        component: FeaturesView,
    },
    {
        path: "/pricing",
        name: "pricing",
        component: PricingView,
    },
    {
        path: "/about",
        name: "about",
        component: AboutView,
    },
    {
        path: "/login",
        name: "login",
        component: AuthenticationView,
    },
]

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export default router;